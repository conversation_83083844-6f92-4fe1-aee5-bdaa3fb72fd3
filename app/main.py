import asyncio
import logging
import time
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI

from app.api.v1.routers import api_router
from app.core.config import settings
from app.core.log import setup_logging
from app.core.signals import add_cleanup_task, cleanup, setup_signal_handlers, wait_for_shutdown
from app.middleware.log import LoggingMiddleware
from app.middleware.request_id import RequestIdMiddleware
from app.middleware.response_headers import ResponseHeadersMiddleware
from app.schemas.common import HealthCheckResponse


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动阶段
    setup_logging()
    setup_signal_handlers()

    # 添加清理任务示例
    def cleanup_logs():
        """清理日志资源"""
        logging.info("正在清理日志资源...")
        # 这里可以添加具体的日志清理逻辑

    def cleanup_connections():
        """清理连接资源"""
        logging.info("正在清理连接资源...")
        # 这里可以添加数据库连接、HTTP连接等清理逻辑

    add_cleanup_task(cleanup_logs)
    add_cleanup_task(cleanup_connections)

    logging.info(
        f"服务正在启动... 环境: {settings.ENVIRONMENT}, 版本: {settings.VERSION}, "
        f"SIGTERM超时: {settings.SIGTERM_TIMEOUT}秒"
    )

    yield

    # 关闭阶段
    logging.info("开始优雅关闭服务...")
    await cleanup()
    logging.info("服务已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="AI Server",
        description="基于FastAPI的AI服务后端项目",
        version=settings.VERSION,
        debug=settings.DEBUG,
        lifespan=lifespan,
    )

    app.add_middleware(LoggingMiddleware)
    app.add_middleware(RequestIdMiddleware)
    app.add_middleware(ResponseHeadersMiddleware)
    #
    app.include_router(api_router)

    return app


app = create_app()


@app.get("/", include_in_schema=False)
def read_root():
    return {"message": "AI Server API", "docs": "/docs", "health": "/health"}


@app.get("/health", response_model=HealthCheckResponse, tags=["系统"])
async def health_check():
    return HealthCheckResponse(
        status="healthy",
        timestamp=time.time(),
        version=settings.VERSION,
        environment=settings.ENVIRONMENT,
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
